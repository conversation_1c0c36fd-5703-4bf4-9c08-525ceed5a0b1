import {
	ICaseDetails,
	IChildFamilyMember,
	IEducationCase,
	IFamilyMember,
} from "interfaces/SocialAidForm.interface";
import { useRouter } from "next/router";
import { Dispatch, SetStateAction, useState } from "react";
import { useMutation } from "react-query";
import { getFormData, modifyRequest } from "services/frontend";
import {
	mapSocialAidFormToCaseForm,
	mapSocialAidFormToEducationMembers,
	mapSocialAidFormToFamilyMembers,
} from "utils/helpers";
import { POD_CHILD_SUBCATEGORY_ID } from "config";

// Track first-time POD Child draft fetches per session (in-memory)
const podChildDraftFirstFetchDone = new Set<string>();

const initialCaseDetails: ICaseDetails = {
	Occupation: "",
	JobTitle: "",
	Alternativenumber: "",
	AlternativeEmail: "",
	Region: "",
	CaseRef: "",
	Education: "",
	MaritalStatus: "",
	AccomodationType: "",
	IsHouseholdHeadContributeToIncome: false,
	ListIncomeSourceDetails: [],
	IsHouseholdHeadReceivePensionIncome: false,
	ListPensionDetails: [],
	IshouseholdHeadTradeLicense: false,
	ListTradeLicenseDetails: [],
	IshouseholdHeadReceiveRentalIncome: false,
	ListRentalDetails: [],
	ListFamilyMember: [],
	//PortalPersona: "",
	//SubPersona: "",
	ChildEligibilityforWomeninDifficulty: "",
	Category: "",
	SubCategory: "",
	Area: "",
	Emirate: "",
	IsActiveStudent: false,
	PursuingHigherEducation: false,
	DraftedinMilitaryService: false,
	ReceivedLocalSupport: 662410001,
	ReceivingFederalLocalhousingsupport: 662410001,
	NumberOfChildren: 0,
	NumberOfChildrenLessThan25: 0,
	//HaveChildrenCustody: 662410001,
	GuardianEmiratesID: "",
};

const modifyDataForRequest = (objectArray: any) => {
	let values = "";
	if (Array.isArray(objectArray)) {
		if (objectArray && objectArray.length > 0) {
			values = objectArray.map((item) => item.value).join(",");
		}
	} else {
		values = objectArray;
	}
	return values;
};

const useSocialAidRequest = (
	caseForm: any,
	setCaseForm: any,
	familyMembers: IFamilyMember[],
	setFamilyMembers: Dispatch<SetStateAction<IFamilyMember[]>>,
	activeIndex: any,
	subIndex: any,
	userDetails: any,
	childMembers: IChildFamilyMember[],
	setChildMembers: any,
	setKhulasitQaidNumber: any,
	documentList?: any,
	educationMembers?: IEducationCase[],
	testing = false,
	isCategoryChange = false,
	setEducationyMembers?: Dispatch<SetStateAction<IEducationCase[]>>
) => {
	const { push: routerPush, query, locale } = useRouter();

	let docIdList: string[] = [];
	let arr1 = [];
	let arr2 = [];
	if (documentList && documentList?.ListAdditionalDoc.length > 0)
		arr1 = documentList?.ListAdditionalDoc.map((item) => item?.IdDocuments);

	if (documentList && documentList?.ListPersonalDocs.length > 0)
		arr2 = documentList?.ListPersonalDocs.map((item) => item?.IdDocuments);

	docIdList = [...arr1, ...arr2];

	const [submitLoading, setSubmitLoading] = useState(false);

	const requestId = query.requestId?.toString() || "";

	const {
		personalInformation,
		socialAidInformation,
		housingInformation,
		incomeInformation,
		inflationInformation,
	} = caseForm;
	const { mutateAsync, isLoading } = useMutation({
		mutationFn: async (submit: boolean = false) => {
			let getDataRequest: any = {
				endpoint: null,
				caseId: requestId,
				beneficiaryId: userDetails?.ContactId || "",
			};

			if (activeIndex === 0) {
				if (subIndex === 0) getDataRequest.endpoint = "Request/GetCasePersonalDetails";
				if (subIndex === 1) getDataRequest.endpoint = "Request/GetCaseFamilyMembersListDetails";
				if (subIndex === 2) getDataRequest.endpoint = "Request/GetCaseFamilyMembersListDetails";
				if (subIndex === 3 || subIndex === 2)
					getDataRequest.endpoint = "Request/GetCaseEducationalAllowance";
			}
			if (activeIndex === 1) getDataRequest.endpoint = "Request/GetCaseSummaryDetails";

			const data = await modifyRequest({
				UpdateType: !requestId ? "CREATE" : submit ? "SUBMIT" : "DRAFT",
				IdCase: requestId || undefined,
				CaseDetails: {
					...initialCaseDetails,
					Occupation: personalInformation.Occupations || "",
					Emirate: personalInformation.Emirates || personalInformation.Emirate || "",
					JobTitle: personalInformation.jobTitle || "",
					Area: personalInformation.Area || "",
					Center: personalInformation.Center || "",
					Alternativenumber: personalInformation.alternativeNumber || "",
					AlternativeEmail: personalInformation.AlternativeEmail || "",
					Region: personalInformation.Region || "",
					CaseRef: personalInformation.caseID || "",
					Education: socialAidInformation.Educations || "",
					MaritalStatus: personalInformation.MaritalStatus || "",
					AccomodationType: socialAidInformation.accomadations || "",
					ChildEligibilityforWomeninDifficulty: modifyDataForRequest(
						socialAidInformation.ChildEligibilityforWomeninDifficulty
					),
					Category: socialAidInformation.Category || "",
					SubCategory: socialAidInformation.SubCategory || "",
					IsActiveStudent: socialAidInformation.IsActiveStudent === "yes",
					// HaveChildrenCustody:
					// 	socialAidInformation.HaveChildrenCustody === "yes"
					// 		? 662410000
					// 		: socialAidInformation.HaveChildrenCustody === "no"
					// 		? 662410001
					// 		: 662410002,
					ReceivedLocalSupport:
						socialAidInformation.ReceivedLocalSupport === "yes"
							? 662410000
							: socialAidInformation.ReceivedLocalSupport === "no"
							? 662410001
							: 662410002,
					PursuingHigherEducation: socialAidInformation.PursuingHigherEducation === "yes",
					NumberOfChildren: socialAidInformation.NumberOfChildren || 0,
					NumberOfChildrenLessThan25: socialAidInformation.NumberOfChildrenLessThan25 || 0,
					DraftedinMilitaryService: socialAidInformation.PursuingMilitaryService === "yes",
					MilitaryServiceStatus: socialAidInformation.MilitaryServiceStatus || "",
					Terminated: socialAidInformation.Terminated || "",
					GuardianEmiratesID: socialAidInformation.GuardianEmiratesID || "",
					IsHouseholdHeadContributeToIncome: incomeInformation.householdHeadContributes === "yes",
					ListofChildren: activeIndex === 0 && subIndex === 2 ? childMembers : [],
					ListUploadedDocuments: docIdList,
					ApplyHousingAllowance: housingInformation.ApplyHousingAllowance === "yes",
					IsHouseholOwnerResidentialProperty:
						housingInformation.IsHouseholOwnerResidentialProperty === "yes",
					LivingSituation: housingInformation.LivingSituation || "",
					ReceivingFederalLocalhousingsupport:
						housingInformation.ReceivingFederalLocalhousingsupport === "yes"
							? 662410000
							: housingInformation.ReceivingFederalLocalhousingsupport === "no"
							? 662410001
							: 662410002,
					ReceivingHousingAllowanceFromEmployer:
						housingInformation.ReceivingHousingAllowanceFromEmployer === "yes",
					ReceivesHousingSupportFromHusband:
						housingInformation.ReceivesHousingSupportFromHusband === "yes",
					IsUtilityBillIssuedForFullyOwnedProperty:
						housingInformation.IsUtilityBillIssuedForFullyOwnedProperty === "yes",
					FullOwnershipResidentialProperty:
						housingInformation.FullOwnershipResidentialProperty === "yes",
					ApplyInflationAllowance: inflationInformation.ApplyInflationAllowance === "yes",
					ApplyUtilityAllowance: inflationInformation.ApplyUtilityAllowance === "yes",
					UtilityProvider: inflationInformation.UtilityProvider || "",
					UtilityAccountNumber: inflationInformation.UtilityAccountNumber || "",
					// ListIncomeSourceDetails:
					// 	incomeInformation.householdHeadContributes === "yes"
					// 		? (incomeInformation.incomes || []).map((i) => ({
					// 				IdIncome: i.IdIncome || undefined,
					// 				IncomeSource: i.IncomeTypes,
					// 				IncomeAmount: i.incomeAmount,
					// 				CompanyName: i.companyName,
					// 		  }))
					// 		: [],

					// IsHouseholdHeadReceivePensionIncome: incomeInformation.householdHeadPension === "yes",
					// ListPensionDetails:
					// 	incomeInformation.householdHeadPension === "yes"
					// 		? (incomeInformation.pensions || []).map((i) => ({
					// 				IdPension: i.IdPension || undefined,
					// 				PensionType: i.PensionType,
					// 				PensionAuthority: i.PensionAuthority,
					// 				IncomeAmount: i.pensionAmount,
					// 		  }))
					// 		: [],

					// IshouseholdHeadTradeLicense: incomeInformation.householdHeadTradeLicense === "yes",
					// ListTradeLicenseDetails:
					// 	incomeInformation.householdHeadTradeLicense === "yes"
					// 		? (incomeInformation.tradeLicenses || []).map((i) => ({
					// 				IdTradeLicense: i.IdTradeLicense || undefined,
					// 				IncomeAmount: i.tradeLicenseAmount,
					// 		  }))
					// 		: [],
					// IshouseholdHeadReceiveRentalIncome: incomeInformation.householdRentalIncomes === "yes",
					// ListRentalDetails:
					// 	incomeInformation.householdRentalIncomes === "yes"
					// 		? (incomeInformation.RentalIncomes || []).map((i) => ({
					// 				IdRental: i.IdRental || undefined,
					// 				ContractNumber: i.ContractNo,
					// 				RentalSource: i.rentalSource,
					// 				ContractStartDate: i.ContractStartDate,
					// 				ContractEndDate: i.ContractEndDate,
					// 				IncomeAmount: i.RentAmount,
					// 		  }))
					// 		: [],
					ListFamilyMember:
						activeIndex === 0 && subIndex === 2
							? [
									...(familyMembers || []).map((i) => ({
										...i,
										ListIncomeSourceDetails: i.IsFamilyMemberContributeToIncome
											? i.ListIncomeSourceDetails
											: [],
										ListPensionDetails: i.IsFamilyMemberReceivePensionIncome
											? i.ListPensionDetails
											: [],
										ListTradeLicenseDetails: i.IsFamilyMemberReceiveTradeLicense
											? i.ListTradeLicenseDetails
											: [],
										ListRentalDetails: i.IsFamilyMemberReceiveRentalIncome
											? i.ListRentalDetails
											: [],
									})),
							  ]
							: [],
					EducationCaseDetails: educationMembers,
				},
				CaseType: 1,
				Index: activeIndex,
				SubIndex: subIndex,
				Testing: testing,
			});

			const resp = data.Data;
			if (!resp?.IdCase) return data;

			// Compute effective isCategoryChange for POD Child draft first fetch
			let effectiveIsCategoryChange = isCategoryChange;
			const isPODChild =
				(caseForm?.socialAidInformation?.SubCategory || socialAidInformation?.SubCategory) ===
				POD_CHILD_SUBCATEGORY_ID;
			const isFamilyMembersEndpoint =
				getDataRequest.endpoint === "Request/GetCaseFamilyMembersListDetails";
			const currentCaseId = getDataRequest.caseId || resp?.IdCase || null;
			if (isFamilyMembersEndpoint && isPODChild && requestId) {
				// Existing draft case with POD Child
				if (!podChildDraftFirstFetchDone.has(requestId)) {
					// First navigation in this session for this draft -> allow CreateFbForCase
					effectiveIsCategoryChange = true;
					podChildDraftFirstFetchDone.add(requestId);
				} else {
					// Subsequent navigations -> preserve original behavior (do not force true)
					effectiveIsCategoryChange = isCategoryChange;
				}
			}

			const query = isFamilyMembersEndpoint
				? {
						caseId: currentCaseId,
						endpoint: getDataRequest.endpoint,
						isCategoryChange: effectiveIsCategoryChange,
				  }
				: {
						caseId: currentCaseId,
						endpoint: getDataRequest.endpoint,
				  };
			const caseDataRequest = await getFormData(query);
			const caseData = caseDataRequest.Data;
			//console.log("data returned caseData", caseData);
			//i did this as per to drop 1 changes to prevent spouses from edit anything / don't create a new api in the backend
			if (caseData?.ListFamilyMember && caseData?.ListFamilyMember?.length > 0) {
				caseData.ListFamilyMember = caseData.ListFamilyMember.map((item) => {
					// if (item?.Relationship === WIFE_LOOKUP_ID) {
					// 	item.IsInformationUpdated = true;
					// }
					item.Fullname = locale === "en" ? item.FullnameEN : item.FullnameAR;
					return item;
				});
			}
			if (!caseData) return caseData;
			// @ts-ignore
			if (data.Data) data.Data.CaseDetails = caseData;
			if (submit) {
				setSubmitLoading(true);
				return data;
			}

			setCaseForm((state) => mapSocialAidFormToCaseForm(state, data?.Data?.CaseDetails));
			if (data?.Data?.CaseDetails?.ListFamilyMember)
				setFamilyMembers(mapSocialAidFormToFamilyMembers(data?.Data?.CaseDetails));
			if (setEducationyMembers && data?.Data?.CaseDetails?.EducationCaseDetails)
				setEducationyMembers(mapSocialAidFormToEducationMembers(data?.Data?.CaseDetails));

			if (data?.Data?.CaseDetails?.ListofChildren)
				setChildMembers(data?.Data?.CaseDetails?.ListofChildren);
			setKhulasitQaidNumber(data?.Data?.CaseDetails?.KhulasitQaidNumber);
			return data;
		},
		mutationKey: "modifyRequest",
		onSuccess: (resp, submit) => {
			if (resp?.IsSuccess && resp?.Data) {
				if (!requestId)
					routerPush(
						`/smart-services/how-to-apply/apply-socialaid?requestId=${resp?.Data?.IdCase}`,
						undefined,
						{
							shallow: true,
						}
					);
				if (submit) {
					routerPush(
						`/smart-services/how-to-apply/apply-socialaid/review-socialaid?requestId=${resp?.Data?.IdCase}`
					);
					return;
				}
			}
		},
	});

	return {
		updateRequest: mutateAsync,
		updateRequestLoading: isLoading || submitLoading,
	};
};

export default useSocialAidRequest;
