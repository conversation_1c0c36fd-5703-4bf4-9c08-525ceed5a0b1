import { format } from "date-fns";
import { ICrmContact } from "interfaces/CrmContact.interface";
import { getToken } from "next-auth/jwt";
import React from "react";
import { enUS, ar } from "date-fns/locale";
import {
	ICrmLookup,
	ICrmLookupLocalized,
	ICrmLookupWithParent,
	ICrmMasterData,
	initialCrmMasterData,
} from "interfaces/CrmMasterData.interface";
import { ICaseDetails } from "interfaces/SocialAidForm.interface";
import * as personalInformationFunctions from "pagesComponents/ApplySocialAid/RequestDetails/PersonalInformationForm/functions";
import { formatAmount } from "./formatters";
import { IFarmerCaseDetails } from "interfaces/FarmerAidForm.interface";

export const onEnterKey = (e: React.KeyboardEvent<HTMLInputElement>, cb: Function) => {
	if (e.key === "Enter") {
		cb();
	}
};

export const getFormattedDate = (
	date: Date | string | undefined,
	dateFormat: string,
	locale: string = "en"
) => {
	if (!date) return "";
	const localeObject = !locale || locale === "en" ? enUS : ar;
	return format(new Date(date), dateFormat, { locale: localeObject });
};

export const formatEmiratesId = (eid: string | undefined) => {
	if (!eid) return "";
	return eid.replace(/(\d{3})-?(\d{4})-?(\d{7})-?(\d)/, "$1-$2-$3-$4");
};

export const getEmiratesIdFromToken = async (req: any) => {
	const token = await getToken({ req });
	const user = token?.user as ICrmContact;

	return user?.EmiratesID;
};

export const getIsExistingBeneficiaryFromToken = async (req: any) => {
	const token = await getToken({ req });
	const user = token?.user as ICrmContact;

	return user?.IsExistingBeneficiary;
};

export const getIsEmiratesIDExpiryDateFromToken = async (req: any) => {
	const token = await getToken({ req });
	const user = token?.user as ICrmContact;

	return user?.EmiratesIDExpiryDate;
};

export const getEligibilityForAllowanceData = async (req: any) => {
	const token = await getToken({ req });
	const user = token?.user as ICrmContact;

	return {
		EligibleHousing: user?.EligibleHousing,
		EligibleEducation: user?.EligibleEducation,
		IdCase: user?.IdCase,
	};
};

export const getIsEmiratesNationalityFromToken = async (req: any) => {
	const token = await getToken({ req });
	const user = token?.user as ICrmContact;

	return user?.IsEmirates;
};

export const getUserInflationEligibilityDetails = async (req: any) => {
	const token = await getToken({ req });
	const user = token?.user as ICrmContact;

	const dataObj = {
		EligibleInflation: user?.EligibleInflation,
		Age: user?.Age,
	};

	return dataObj;
};

export const getContactIdFromToken = async (req: any) => {
	const token = await getToken({ req });
	const user = token?.user as ICrmContact;
	return user?.ContactId;
};

export const formatUaePhoneNumberIntl = (phone: string | undefined) => {
	if (!phone) return "+971";
	phone = phone.replaceAll(" ", "");
	if (phone.startsWith("05")) phone = phone.replace(/^05/, "+9715");
	return phone;
};

export const formatUaePhoneNumber = (phone: string | undefined) => {
	phone = formatUaePhoneNumberIntl(phone);

	return phone.replace(/\+(\d{3})?(\d{2})?(\d{3})?(\d{4})?/, "+$1 $2 $3 $4");
};

export const getLocalizedRequestName = (name: string, requestI18n: string) => {
	return name.replace("Request - ", `${requestI18n} - `);
};

export const getLocalizedFullName = (user: ICrmContact, locale: string = "ar") => {
	if (!user) return "";
	return locale === "en"
		? `${user?.FirstName || ""} ${user?.MiddleName || ""} ${user?.LastName || ""}`
		: `${user?.FirstNameArabic || ""} ${user?.MiddleNameArabic || ""} ${
				user?.LastNameArabic || ""
		  }`;
};

export const getNewLocalizedFullName = (user: ICrmContact, locale: string = "ar") => {
	if (!user) return "";
	return locale === "en" ? `${user?.LastName || ""}` : `${user?.FullNameArabic || ""}`;
};

export const formatCurrencyAmount = (
	amount: string | undefined,
	t: any,
	locale: string | undefined
) => {
	let currencyLocalized = t("tables:aed") || "AED";
	return `${locale === "en" ? `${currencyLocalized} ` : ""}${formatAmount(amount || "0")}${
		locale === "ar" ? ` ${currencyLocalized}` : ""
	}`;
};

export const getSpaceSeparatedString = (strArr: any[]) => {
	return strArr.reduce((acc, curr) => {
		if (curr) return `${acc} ${curr}`;
		return acc;
	}, "");
};

export const validateEmail = (email: string) => {
	const re = /\S+@\S+\.\S+/;
	return re.test(email);
};

export const validateUaePhoneNumber = (phone: string) => {
	const re = /^\+9715\d{8}$/;
	return re.test(phone);
};

export const toBase64DataUri = (file: File): Promise<string> =>
	new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = () => {
			const data = reader.result?.toString().split(",")[1] || "";
			resolve(data);
		};
		reader.onerror = (error) => reject(error);
	});

export const fileToByteArray = (file: File) => {
	return new Promise((resolve, reject) => {
		try {
			let reader = new FileReader();
			let fileByteArray: any = [];
			reader.readAsArrayBuffer(file);
			reader.onloadend = (evt) => {
				if (evt.target?.readyState == FileReader.DONE) {
					let arrayBuffer: any = evt.target.result,
						array = new Uint8Array(arrayBuffer);
					// for (byte of array) {
					// 	fileByteArray.push(byte);
					// }
					array.forEach((byte) => {
						fileByteArray.push(byte);
					});
				}
				resolve(fileByteArray);
			};
		} catch (e) {
			reject(e);
		}
	});
};

export const getBase64DataUri = (base64: string) => {
	return `data:image;base64,${base64}`;
};

export const downloadBase64File = (contentType: string, base64Data: string, fileName: string) => {
	const linkSource = `data:${contentType};base64,${base64Data}`;
	const downloadLink = document.createElement("a");
	downloadLink.href = linkSource;
	downloadLink.download = fileName;
	downloadLink.click();
};

export const getLookupItem = (lookup: Object, field: string, value: string | undefined) => {
	if (!value) return {};
	const label = lookup[field].find((item: any) => item.value === value)?.label;
	if (!label) return {};
	return {
		value,
		label,
	};
};

export const getStaticLookupItem = (lookup: any, value: string | undefined) => {
	if (!value) return {};
	const label = lookup.find((item: any) => item.value === value.toString())?.label;
	if (!label) return {};

	return {
		value,
		label,
	};
};

export const getLookupLabel = (lookup: Object, field: string, value: string | undefined) => {
	if (!value) return "";
	return lookup[field]?.find((item: any) => item.value === value)?.label;
};

export const getLocalizedLookups = (
	lookups: ICrmMasterData<ICrmLookup | ICrmLookupWithParent>,
	locale: string
) => {
	const localizedLookups = {};
	for (const key in lookups) {
		localizedLookups[key] = lookups[key].map((item: ICrmLookup | ICrmLookupWithParent) => {
			return {
				value: item.Id,
				label: locale === "en" ? item.Name || item.NameAR : item.NameAR || item.Name,
				RelatedId: "RelatedId" in item ? item.RelatedId : null,
				Code: item.Code ? item.Code : null,
			};
		});
	}
	return localizedLookups as ICrmMasterData<ICrmLookupLocalized>;
};

export const addLocalLookups = (
	lookups: ICrmMasterData<ICrmLookup | ICrmLookupWithParent>
): ICrmMasterData<ICrmLookup | ICrmLookupWithParent> => {
	return {
		...lookups,
		Boolean: initialCrmMasterData.Boolean,
		//@ts-ignore
		IncomeTypes: lookups.IncomeSources,
	};
};

export const mapSocialAidFormToCaseForm = (
	state: any,
	CaseDetails?: ICaseDetails | undefined,
	userDetails?: any
) => {
	let data = {
		personalInformation: {},
		socialAidInformation: {},
		housingInformation: {},
		inflationInformation: {},
		incomeInformation: {},
		...state,
	};

	if (userDetails) {
		for (let key in personalInformationFunctions.getInitialValues) {
			if (key in userDetails) {
				data.personalInformation[key] = userDetails[key];
			}
		}
		data.personalInformation["PreferredEmail"] = userDetails.ICAEmail;
		data.personalInformation["AlternativeEmail"] = userDetails.PreferredEmail;
		data.personalInformation["PreferredPhoneNumber"] = userDetails.ICAPhoneNumber;
		data.personalInformation["alternativeNumber"] = userDetails.PreferredPhoneNumber;
		data.personalInformation["MaritalStatus"] = userDetails.MaritalStatus.MaritalStatusId;
	}

	const _findValue = (apiValue, stateValue) => {
		if (apiValue === true) return "yes";
		if (apiValue === false) return "no";
		if (stateValue === "yes") return "yes";
		if (stateValue === "no") return "no";
		return null;
	};

	const _findNumberValue = (apiValue, stateValue) => {
		if (apiValue === 662410000) return "yes";
		if (apiValue === 662410001) return "no";
		if (stateValue === "yes") return "yes";
		if (stateValue === "no") return "no";
		return null;
	};

	const _findStringValue = (stateValue) => {
		let values = stateValue;
		if (Array.isArray(stateValue)) {
			if (stateValue && stateValue.length > 0) {
				values = stateValue.map((item) => item.value).join(",");
			}
		}
		return values;
	};

	let mrStatus;
	if (
		CaseDetails?.MaritalStatus &&
		CaseDetails?.MaritalStatus !== "00000000-0000-0000-0000-000000000000"
	) {
		mrStatus = CaseDetails?.MaritalStatus;
	} else if (
		state?.personalInformation?.MaritalStatus &&
		state?.personalInformation?.MaritalStatus !== "00000000-0000-0000-0000-000000000000"
	) {
		mrStatus = state?.personalInformation?.MaritalStatus;
	} else if (
		userDetails?.MaritalStatus?.MaritalStatusId &&
		userDetails?.MaritalStatus?.MaritalStatusId !== "00000000-0000-0000-0000-000000000000"
	) {
		mrStatus = userDetails?.MaritalStatus?.MaritalStatusId;
	} else {
		mrStatus = "";
	}

	if (CaseDetails) {
		data = {
			...data,
			personalInformation: {
				...data.personalInformation,
				Occupations: CaseDetails?.Occupation || state?.personalInformation?.Occupations,
				Emirates: CaseDetails?.Emirate || state?.personalInformation?.Emirates,
				jobTitle: CaseDetails?.JobTitle || state?.personalInformation?.jobTitle,
				alternativeNumber:
					CaseDetails?.Alternativenumber || state?.personalInformation?.alternativeNumber,
				caseID: CaseDetails?.CaseRef || state?.personalInformation?.caseID,
				AlternativeEmail:
					CaseDetails?.AlternativeEmail || state?.personalInformation?.AlternativeEmail,
				Area: CaseDetails?.Area || state?.personalInformation?.Area,
				Center: CaseDetails?.Center || state?.personalInformation?.Center,
				MaritalStatus: mrStatus,
				hasMaritalStatus: mrStatus !== "",
			},
			socialAidInformation: {
				...data.socialAidInformation,
				Educations: CaseDetails.Education || state?.socialAidInformation?.Educations,
				accomadations: CaseDetails.AccomodationType || state?.socialAidInformation?.accomadations,
				//MaritalStatus: mrStatus,
				hasMaritalStatus: mrStatus !== "",
				//PortalPersona: CaseDetails.PortalPersona || state?.socialAidInformation?.PortalPersona,
				//SubPersona: CaseDetails.SubPersona || state?.socialAidInformation?.SubPersona,
				Category: CaseDetails.Category || state?.socialAidInformation?.Category,
				NumberOfChildren:
					CaseDetails.NumberOfChildren || state?.socialAidInformation?.NumberOfChildren,
				NumberOfChildrenLessThan25:
					CaseDetails.NumberOfChildrenLessThan25 ||
					state?.socialAidInformation?.NumberOfChildrenLessThan25,
				SubCategory: CaseDetails.SubCategory || state?.socialAidInformation?.SubCategory,
				IsActiveStudent: _findValue(
					CaseDetails.IsActiveStudent,
					state?.socialAidInformation?.IsActiveStudent
				),
				PursuingHigherEducation: _findValue(
					CaseDetails.PursuingHigherEducation,
					state?.socialAidInformation?.PursuingHigherEducation
				),
				PursuingMilitaryService: _findValue(
					CaseDetails.DraftedinMilitaryService,
					state?.socialAidInformation?.PursuingMilitaryService
				),
				MilitaryServiceStatus:
					CaseDetails.MilitaryServiceStatus || state?.socialAidInformation?.MilitaryServiceStatus,
				Terminated: CaseDetails.Terminated || state?.socialAidInformation?.Terminated,
				ChildEligibilityforWomeninDifficulty: _findStringValue(
					CaseDetails.ChildEligibilityforWomeninDifficulty ||
						state?.socialAidInformation?.ChildEligibilityforWomeninDifficulty ||
						""
				),
				// HaveChildrenCustody: _findNumberValue(
				// 	CaseDetails.HaveChildrenCustody,
				// 	state?.socialAidInformation?.HaveChildrenCustody
				// ),
				ReceivedLocalSupport: _findNumberValue(
					CaseDetails.ReceivedLocalSupport,
					state?.socialAidInformation?.ReceivedLocalSupport
				),
				GuardianEmiratesID:
					CaseDetails?.GuardianEmiratesID || state?.socialAidInformation?.GuardianEmiratesID || "",
			},
			housingInformation: {
				ApplyHousingAllowance: _findValue(
					CaseDetails.ApplyHousingAllowance,
					state?.housingInformation?.ApplyHousingAllowance
				),
				ReceivingHousingAllowanceFromEmployer: _findValue(
					CaseDetails.ReceivingHousingAllowanceFromEmployer,
					state?.housingInformation?.ReceivingHousingAllowanceFromEmployer
				),
				IsHouseholOwnerResidentialProperty: _findValue(
					CaseDetails.IsHouseholOwnerResidentialProperty,
					state?.housingInformation?.IsHouseholOwnerResidentialProperty
				),
				ReceivingFederalLocalhousingsupport: _findNumberValue(
					CaseDetails.ReceivingFederalLocalhousingsupport,
					state?.housingInformation?.ReceivingFederalLocalhousingsupport
				),
				LivingSituation: CaseDetails.AccomodationType || state?.housingInformation?.LivingSituation,
				ReceivesHousingSupportFromHusband: _findValue(
					CaseDetails.ReceivesHousingSupportFromHusband,
					state?.housingInformation?.ReceivesHousingSupportFromHusband
				),
				IsUtilityBillIssuedForFullyOwnedProperty: _findValue(
					CaseDetails.IsUtilityBillIssuedForFullyOwnedProperty,
					state?.housingInformation?.IsUtilityBillIssuedForFullyOwnedProperty
				),
				FullOwnershipResidentialProperty: _findValue(
					CaseDetails.FullOwnershipResidentialProperty,
					state?.housingInformation?.FullOwnershipResidentialProperty
				),
			},
			inflationInformation: {
				ApplyInflationAllowance: _findValue(
					CaseDetails.ApplyInflationAllowance,
					state?.inflationInformation?.ApplyInflationAllowance
				),
				ApplyUtilityAllowance: _findValue(
					CaseDetails.ApplyUtilityAllowance,
					state?.inflationInformation?.ApplyUtilityAllowance
				),
				UtilityProvider:
					CaseDetails.UtilityProvider || state?.inflationInformation?.UtilityProvider,
				UtilityAccountNumber:
					CaseDetails.UtilityAccountNumber || state?.inflationInformation?.UtilityAccountNumber,
			},
			incomeInformation: {
				...data.incomeInformation,
				householdHeadContributes: _findValue(
					CaseDetails?.IsHouseholdHeadContributeToIncome,
					state?.incomeInformation?.householdHeadContributes
				),
				incomes:
					CaseDetails.ListIncomeSourceDetails?.map((i) => ({
						IdIncome: i.IdIncome,
						IncomeTypes: i.IncomeSource,
						incomeAmount: i.IncomeAmount,
						companyName: i.CompanyName,
					})) || state?.incomeInformation?.incomes,
				householdHeadPension: _findValue(
					CaseDetails?.IsHouseholdHeadReceivePensionIncome,
					state?.incomeInformation?.householdHeadPension
				),
				pensions:
					CaseDetails.ListPensionDetails?.map((i) => ({
						IdPension: i.IdPension,
						PensionType: i.PensionType,
						PensionAuthority: i.PensionAuthority,
						pensionAmount: i.IncomeAmount,
					})) || state?.incomeInformation?.pensions,
				householdHeadTradeLicense: _findValue(
					CaseDetails?.IshouseholdHeadTradeLicense,
					state?.incomeInformation?.householdHeadTradeLicense
				),
				tradeLicenses:
					CaseDetails.ListTradeLicenseDetails?.map((i) => ({
						IdTradeLicense: i.IdTradeLicense,
						tradeLicenseAmount: i.IncomeAmount,
					})) || state?.incomeInformation?.tradeLicenses,
				householdRentalIncomes: _findValue(
					CaseDetails?.IshouseholdHeadReceiveRentalIncome,
					state?.incomeInformation?.householdRentalIncomes
				),
				RentalIncomes:
					CaseDetails.ListRentalDetails?.map((i) => ({
						IdRental: i.IdRental,
						ContractNo: i.ContractNumber,
						rentalSource: i.RentalSource,
						ContractStartDate: i.ContractStartDate,
						ContractEndDate: i.ContractEndDate,
						RentAmount: i.IncomeAmount,
					})) || state?.incomeInformation?.RentalIncomes,
			},
		};
	}
	//console.log("after fill data", data);
	return data;
};

export const mapSocialAidFormToFamilyMembers = (
	CaseDetails?: ICaseDetails | IFarmerCaseDetails | undefined
) => {
	return [...(CaseDetails?.ListFamilyMember || [])];
};

export const mapSocialAidFormToEducationMembers = (CaseDetails?: ICaseDetails | undefined) => {
	return [...(CaseDetails?.EducationCaseDetails || [])];
};

// Inflation mapping
export const mapInflationFormToCaseForm = (
	state: any,
	CaseDetails?: ICaseDetails | undefined,
	userDetails?: any
) => {
	let data = {
		personalInformation: {},
		informationForm: {},
		inflationInformation: {},
		...state,
	};

	if (userDetails) {
		for (let key in personalInformationFunctions.getInitialValues) {
			if (key in userDetails) {
				data.personalInformation[key] = userDetails[key];
			}
		}
		data.personalInformation["PreferredEmail"] = userDetails.ICAEmail;
		data.personalInformation["AlternativeEmail"] = userDetails.PreferredEmail;
		data.personalInformation["PreferredPhoneNumber"] = userDetails.ICAPhoneNumber;
		data.personalInformation["alternativeNumber"] = userDetails.PreferredPhoneNumber;
		data.personalInformation["MaritalStatus"] = userDetails.MaritalStatus.MaritalStatusId;
	}

	const _findValue = (apiValue, stateValue) => {
		if (apiValue === true) return "yes";
		if (apiValue === false) return "no";
		if (stateValue === "yes") return "yes";
		if (stateValue === "no") return "no";
		return null;
	};

	const _findNumberValue = (apiValue, stateValue) => {
		if (apiValue === 662410000) return "yes";
		if (apiValue === 662410001) return "no";
		if (stateValue === "yes") return "yes";
		if (stateValue === "no") return "no";
		return null;
	};

	const _findStringValue = (stateValue) => {
		let values = stateValue;
		if (Array.isArray(stateValue)) {
			if (stateValue && stateValue.length > 0) {
				values = stateValue.map((item) => item.value).join(",");
			}
		}
		return values;
	};

	let mrStatus;
	if (
		CaseDetails?.MaritalStatus &&
		CaseDetails?.MaritalStatus !== "00000000-0000-0000-0000-000000000000"
	) {
		mrStatus = CaseDetails?.MaritalStatus;
	} else if (
		state?.personalInformation?.MaritalStatus &&
		state?.personalInformation?.MaritalStatus !== "00000000-0000-0000-0000-000000000000"
	) {
		mrStatus = state?.personalInformation?.MaritalStatus;
	} else if (
		userDetails?.MaritalStatus?.MaritalStatusId &&
		userDetails?.MaritalStatus?.MaritalStatusId !== "00000000-0000-0000-0000-000000000000"
	) {
		mrStatus = userDetails?.MaritalStatus?.MaritalStatusId;
	} else {
		mrStatus = "";
	}

	if (CaseDetails) {
		data = {
			...data,
			personalInformation: {
				...data.personalInformation,
				Occupations: CaseDetails?.Occupation || state?.personalInformation?.Occupations,
				Emirates: CaseDetails?.Emirate || state?.personalInformation?.Emirates,
				jobTitle: CaseDetails?.JobTitle || state?.personalInformation?.jobTitle,
				alternativeNumber:
					CaseDetails?.Alternativenumber || state?.personalInformation?.alternativeNumber,
				caseID: CaseDetails?.CaseRef || state?.personalInformation?.caseID,
				AlternativeEmail:
					CaseDetails?.AlternativeEmail || state?.personalInformation?.AlternativeEmail,
				Area: CaseDetails?.Area || state?.personalInformation?.Area,
				Center: CaseDetails?.Center || state?.personalInformation?.Center,
				MaritalStatus: mrStatus,
				hasMaritalStatus: mrStatus !== "",
			},
			informationForm: {
				...data.informationForm,
				InflationCategory:
					CaseDetails.InflationCategory || state?.informationForm?.InflationCategory,
			},
			inflationInformation: {
				ApplyUtilityAllowance: _findValue(
					CaseDetails.ApplyUtilityAllowance,
					state?.inflationInformation?.ApplyUtilityAllowance
				),
				UtilityProvider:
					CaseDetails.UtilityProvider || state?.inflationInformation?.UtilityProvider,
				UtilityAccountNumber:
					CaseDetails.UtilityAccountNumber || state?.inflationInformation?.UtilityAccountNumber,
			},
		};
	}
	return data;
};

export const mapInflationFormToFamilyMembers = (
	CaseDetails?: ICaseDetails | IFarmerCaseDetails | undefined
) => {
	return [...(CaseDetails?.ListFamilyMember || [])];
};

export const mapInflationFormToEducationMembers = (CaseDetails?: ICaseDetails | undefined) => {
	return [...(CaseDetails?.EducationCaseDetails || [])];
};

const tryParseJson = (string: string) => {
	try {
		return JSON.parse(string);
	} catch (e) {
		return string;
	}
};

export const handleApiErrorMessage = (
	errorStr: string | null | undefined,
	toast: Function,
	t: Function,
	locale: string = "ar"
) => {
	const errors = tryParseJson(errorStr || "");
	let description = t("common:genericErrorDescription");
	if (errors) {
		description = (locale === "en" ? errors?.MessageEn : errors?.MessageAr) || description;
	}
	toast({
		title: t("common:genericErrorTitle"),
		description,
		status: "error",
	});
};
export const mapFarmerAidFromCaseForm = (
	state: any,
	CaseDetails?: IFarmerCaseDetails | undefined,
	userDetails?: any,
	locale = "ar"
) => {
	let data = {
		personalInformation: {},
		socialAidInformation: {},
		incomeInformation: {},
		...state,
	};

	if (userDetails) {
		for (let key in personalInformationFunctions.getInitialValues) {
			if (key in userDetails) {
				data.personalInformation[key] = userDetails[key];
			}
		}
		data.personalInformation["PreferredEmail"] = userDetails.ICAEmail;
		data.personalInformation["AlternativeEmail"] = userDetails.PreferredEmail;
		data.personalInformation["PreferredPhoneNumber"] = userDetails.ICAPhoneNumber;
		data.personalInformation["alternativeNumber"] = userDetails.PreferredPhoneNumber;
		data.personalInformation["FirstName"] = userDetails.FirstNameArabic || userDetails.FirstName;
		data.personalInformation["LastName"] = userDetails.LastNameArabic || userDetails.LastName;
	}

	const _findValue = (apiValue, stateValue) => {
		if (apiValue === true) return "yes";
		if (apiValue === false) return "no";
		if (stateValue === "yes") return "yes";
		if (stateValue === "no") return "no";
		return null;
	};

	if (CaseDetails) {
		data = {
			...data,
			personalInformation: {
				...data.personalInformation,
				alternativeNumber:
					CaseDetails?.Alternativenumber || state?.personalInformation?.alternativeNumber,
				caseID: CaseDetails?.CaseRef || state?.personalInformation?.caseID,
				AlternativeEmail:
					CaseDetails?.AlternativeEmail || state?.personalInformation?.AlternativeEmail,
			},
			socialAidInformation: {
				...data.socialAidInformation,
				RegisteredWithEWE:
					CaseDetails?.RegisteredWithEWE ?? state?.socialAidInformation?.RegisteredWithEWE,
				OwnerEWEBill: CaseDetails?.OwnerEWEBill ?? state?.socialAidInformation?.OwnerEWEWBill,
				ReceiveSocialAid:
					CaseDetails?.ReceiveSocialAid ?? state?.socialAidInformation?.ReceiveSocialAid,

				EntityReceivedFrom:
					CaseDetails?.EntityReceivedFrom ?? state?.socialAidInformation?.EntityReceivedFrom,

				ReceiveInflationAllowance:
					CaseDetails?.ReceiveInflationAllowance ??
					state?.socialAidInformation?.ReceiveInflationAllowance,

				EWEBill: CaseDetails?.EWEBill || state?.socialAidInformation?.EWEBill,

				RelatedEmiratesID:
					CaseDetails.RelatedEmiratesID || state?.socialAidInformation.RelatedEmiratesID,
			},
			incomeInformation: {
				...data.incomeInformation,
				householdHeadContributes: _findValue(
					CaseDetails?.IsHouseholdHeadContributeToIncome,
					state?.incomeInformation?.householdHeadContributes
				),
				incomes:
					CaseDetails.ListIncomeSourceDetails?.map((i) => ({
						IdIncome: i.IdIncome,
						IncomeTypes: i.IncomeSource,
						incomeAmount: i.IncomeAmount,
						companyName: i.CompanyName,
					})) || state?.incomeInformation?.incomes,
				householdHeadPension: _findValue(
					CaseDetails?.IsHouseholdHeadReceivePensionIncome,
					state?.incomeInformation?.householdHeadPension
				),
				pensions:
					CaseDetails.ListPensionDetails?.map((i) => ({
						IdPension: i.IdPension,
						PensionType: i.PensionType,
						PensionAuthority: i.PensionAuthority,
						pensionAmount: i.IncomeAmount,
					})) || state?.incomeInformation?.pensions,
				householdHeadTradeLicense: _findValue(
					CaseDetails?.IshouseholdHeadTradeLicense,
					state?.incomeInformation?.householdHeadTradeLicense
				),
				tradeLicenses:
					CaseDetails.ListTradeLicenseDetails?.map((i) => ({
						IdTradeLicense: i.IdTradeLicense,
						tradeLicenseAmount: i.IncomeAmount,
					})) || state?.incomeInformation?.tradeLicenses,
				householdRentalIncomes: _findValue(
					CaseDetails?.IshouseholdHeadReceiveRentalIncome,
					state?.incomeInformation?.householdRentalIncomes
				),
				RentalIncomes:
					CaseDetails.ListRentalDetails?.map((i) => ({
						IdRental: i.IdRental,
						ContractNo: i.ContractNumber,
						rentalSource: i.RentalSource,
						ContractStartDate: i.ContractStartDate,
						ContractEndDate: i.ContractEndDate,
						RentAmount: i.IncomeAmount,
					})) || state?.incomeInformation?.RentalIncomes,
			},
		};
	}

	return data;
};
