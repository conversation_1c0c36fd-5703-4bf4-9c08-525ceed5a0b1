export interface InflationForm {
	UpdateType?: string;
	ParentCaseId?: string;
	eligibleHousing?: boolean;
	eligibleEducation?: boolean;
	IdCase?: string;
	CaseDetails?: ICaseDetails;
	IdProcessTempalte?: string;
	IdAllowanceCategory?: string;
	IdBeneficiary?: string;
	SubmissionTime?: string;
	Index?: number;
	SubIndex?: number;
	Mobile?: string;
	ListFamilyMember?: any;
	Email?: string;
	CaseRef?: string;
	IdStatus?: string;
	Testing?: boolean;
	TotalRefundAmount?: number;
	IsPendingRefund?: boolean;
	CaseType?: number;
	IsInflationStandAloneEdit?: boolean;
	IsInflationBaseEdit?: boolean;
	IsInflationNominatedCaseEdit?: boolean;
}

export interface InflationFormRequest extends Omit<InflationForm, "IdCase"> {
	IdCase?: string;
	Mobile?: string;
	Email?: string;
	CaseRef?: string;
}

export interface InflationFormFERequest
	extends Omit<
		InflationFormRequest,
		"IdBeneficiary" | "IdProcessTempalte" | "IdAllowanceCategory"
	> { }

export interface ICaseDetails {
	Occupation?: string;
	Emirate?: string;
	JobTitle?: string;
	Alternativenumber?: string;
	AlternativeEmail?: string;
	Region?: string;
	CaseRef?: string;
	Education?: string;
	MaritalStatus?: string;
	AccomodationType?: string;
	IsHouseholdHeadContributeToIncome?: boolean;
	ListIncomeSourceDetails?: ListIncomeSourceDetail[];
	IsHouseholdHeadReceivePensionIncome?: boolean;
	ListPensionDetails?: ListPensionDetail[];
	IshouseholdHeadTradeLicense?: boolean;
	ListTradeLicenseDetails?: ListTradeLicenseDetail[];
	IshouseholdHeadReceiveRentalIncome?: boolean;
	ListRentalDetails?: ListRentalDetail[];
	ListFamilyMember?: IFamilyMember[];
	Area?: string;
	//SubPersona?: string;
	KhulasitQaidNumber?: string;
	ChildEligibilityforWomeninDifficulty?: string;
	Category?: string;
	SubCategory?: string;
	Terminated?: string;
	MilitaryServiceStatus?: string;
	IsActiveStudent?: boolean;
	ListUploadedDocuments?: string[];
	//PortalPersona?: string;
	Center?: string;
	//HaveChildrenCustody?: number;
	ReceivedLocalSupport?: number;
	PursuingHigherEducation?: boolean;
	DraftedinMilitaryService?: boolean;
	GuardianEmiratesID?: string;
	ApplyHousingAllowance?: boolean;
	LivingSituation?: string;
	IsHouseholOwnerResidentialProperty?: boolean;
	ReceivingFederalLocalhousingsupport?: number;
	ReceivingHousingAllowanceFromEmployer?: boolean;
	ReceivesHousingSupportFromHusband?: boolean;
	IsUtilityBillIssuedForFullyOwnedProperty?: boolean;
	FullOwnershipResidentialProperty?: boolean;
	NumberOfChildren?: number;
	NumberOfChildrenLessThan25?: number;
	IsUpdate?: boolean;
	ListofChildren?: IChildFamilyMember[];
	InflationCategory?: string;
	UpdateReason?: string;
	// EducationCaseDetails?: IEducationCase[];
}

export interface IChildFamilyMember {
	Id: string;
	IdDependentBeneficary: string;
	IsPursuingHigherEducation?: boolean;
	IsDraftedinMilitaryService?: boolean;
	Age?: number;
	Occupations?: string;
}
export interface ListIncomeSourceDetail {
	IdIncome: string;
	isRemoved: boolean;
	IncomeSource: string;
	IncomeAmount: number;
	CompanyName: string;
}

export interface ListPensionDetail {
	IdPension: string;
	isRemoved: boolean;
	PensionType: string;
	PensionAuthority: string;
	IncomeAmount: number;
}

export interface ListTradeLicenseDetail {
	IdTradeLicense: string;
	isRemoved: boolean;
	IncomeAmount: number;
}

export interface ListRentalDetail {
	IdRental: string;
	isRemoved: boolean;
	IncomeAmount: number;
	ContractNumber: string;
	ContractStartDate: string;
	ContractEndDate: string;
	RentalSource: string;
}

export interface IFamilyMember {
	Id: string;
	IdDependentBeneficary: string;
	IsInformationUpdated: boolean;
	Age?: number;
	Fullname: string;
	FullnameAR?: string;
	FullnameEN?: string;
	Relationship: string;
	Occupations?: string;
	IsFamilyMemberContributeToIncome: boolean;
	ListIncomeSourceDetails: ListIncomeSourceDetail[];
	IsFamilyMemberReceivePensionIncome: boolean;
	ListPensionDetails: ListPensionDetail[];
	IsFamilyMemberReceiveTradeLicense: boolean;
	ListTradeLicenseDetails: ListTradeLicenseDetail[];
	IsFamilyMemberReceiveRentalIncome: boolean;
	ListRentalDetails: ListRentalDetail[];
}

// export interface IEducationCase {
// 	FullNameAr?: string;
// 	FullNameEn?: string;
// 	ApplyEducationAllowance?: boolean;
// 	childCompletedSemesterInUniversity?: boolean;
// 	highSchoolCurriculuim?: string;
// 	enrolledEducationStream?: string;
// 	EmSATorAdvancedPlacementScores?: string;
// 	IsCompletedFromPortal?: boolean;
// 	Age?: string;
// 	IdChild?: string;
// }
