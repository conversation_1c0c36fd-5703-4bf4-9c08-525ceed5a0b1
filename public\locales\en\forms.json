{"accomadations": "Beneficiary's Accommodation Type", "actions": "Actions", "Action": "Actions", "editReason": "Reason to Edit", "reasonToEdit": "Reason to Edit", "additionalAttachments": "Additional Attachments", "additionalDocuments": "Additional Documents", "additionalDocumentsSubtext": "If you wish to upload additional supporting documents with your application, you may do so below.", "addMoreIncome": "Add more Income", "addPension": "Add Pension", "addRentalIncome": "Add Rental Income", "address": "Address (Area, Street Number, House Number)", "addTradeLicense": "Add Trade License", "AlternativeEmail": "Prefered Email", "alternativeNumber": "Preferred Number", "area": "Area", "attachedDocuments": "Attached Documents", "attachedDocumentsSubtext": "Upload the necessary documents below.", "beneficiaryEducationTooltip": "Please specify the highest level of education certificate you have achieved.", "caseID": "Case Number", "caseReason": "Case Reason", "center": "Center", "comment": "Comment", "companyName": "Company Name", "complaintDetails": "{{inquiryType}} Details", "complaintType": "Complaint Type", "complete": "Complete", "ContractEndDate": "Contract End Date", "ContractNo": "Contract Number", "ContractStartDate": "Contract Start Date", "countryOfBirth": "Country of Birth", "dateOfBirth": "Date Of Birth", "deleteCantBeUndone": "This action can't be undone", "deleteFamilyMember": "Delete Family Member", "deleteFamilyMemberText": "Are you sure you want to delete the Family Member?", "deleteIncome": "Delete Income", "deleteIncomeText": "Are you sure you want to delete the additonal Income?", "deletePension": "Delete Pension", "deletePensionText": "Are you sure you want to delete the additonal Pension?", "deleteRentalIncome": "Delete Rental Income", "ThisFieldShouldbeNumber": "The field accepts integer numbers only", "deleteRentalIncomeText": "Are you sure you want to delete the Rental Income?", "deleteTradeLicense": "Delete Trade License", "deleteTradeLicenseText": "Are you sure you want to delete the additonal Trade License?", "deletingFile": "Deleting File", "docd-ad": "Department of Community development - Abu Dhabi", "docd-dubait": "Community Development Authority – Dubai ", "documents": "Documents", "DescriptionLimitationMsg": "Description must be at most 700 characters", "doesFamilyMemberContribute": "Does this family member contribute to the family's income?", "doesFamilyMemberContributeTooltip": "Employment income, including irregular payments such as commissions, bonuses, etc. Please add additional income sources including income from self-employment/ freelancing/ contract agreements, including irregular payments such as income from freelance projects, etc.", "doesFamilyMemberPension": "Does this family member receive a retirement or pension income?", "doesFamilyMemberPensionTooltip": "Income from (public) pension benefit schemes for all household members including the head and contributors (e.g. wives)", "doesFamilyMemberRental": "Does this family member have a Rental Income?", "doesFamilyTradeLicense": "Does this family member have a trade license?", "dropFile": "or drop file here", "editFamilyMembersInformation": "Edit Family Members Information", "education": "Education", "Educations": "Highest Education", "Emirate": "Emirate", "Emirates": "Emirate", "EmiratesID": "Emirates ID", "emiratesID": "Emirates ID", "endDateCantStartBeforeStartDate": "The end date should be after the start date.", "enterRequestDetails": "Enter Case Details", "EWEBill": "Bill number from EWE", "eweErrorMessage": "Please enter valid 12 digit number", "familyMemberInformation": "Family Member Information", "familyMembersInformation": "Family Members Information", "housingInformation": "Housing allowance", "systemValidation": "System Validation", "documentGeneration": "Document Generation", "inflationInformation": "Inflation Information", "educationInformation": "Education Excellence for Higher Education", "familyMembersInformationSubtext": "Please enter all required information below. All fields marked with an asterisk (*) are mandatory.", "filesType": "File types {{extensions}}", "fileUploadErrorFileSize": "Please upload a file with size less than {{size}}", "fileUploadErrorFileTypes": "Please upload a file with the following extensions: {{extensions}}", "FirstName": "First Name", "Fullname": "Full Name", "FullName": "Full Name", "firstName": "First Name", "last": "Full Name", "gender": "Gender", "healthCardInsurance": "Valid Health Card Insurance", "householdHeadContributes": "Does the household head contribute to the family's income?", "householdHeadContributesTooltip": "Employment income, including irregular payments such as commissions, bonuses, etc. Please add additional income sources including income from self-employment / freelancing / contract agreements, including irregular payments such as income from freelance projects, etc.", "householdHeadPension": "Does the household head receive a retirement or pension income?", "householdHeadPensionTooltip": "Income from (public) pension benefit schemes for all household members including the head and contributors (e.g. wives)", "householdHeadTradeLicense": "Does the household head have a trade license?", "householdRentalIncomes": "Does the household head have a Rental Income?", "IDNBackNumber": "EID Card Number (located on the back of the card)", "income": "Income", "incomeAmount": "Income Amount Monthly (in AED)", "Income": "Income Amount Monthly (in AED)", "incomeAmountToolTip": "Please specify the income amount for all household members including the head and contributors (e.g. wives)", "incomeInformation": "Income Information", "incomeInformationSubtext": "Please enter all required information below. All fields marked with an asterisk (*) are mandatory.", "incomeSource": "Income Source", "IncomeSource": "Income Source", "IncomeSourceText": "Income Source", "incomeSourceToolTip": "Please specify the income source type for all household members including the head and contributors (e.g. wives)", "IncomeTypes": "Income Source", "InvalidNumberOfChildren": "The value must be greater than zero, greater than, or equal to the number of criteria selected", "PleaseEnterNumbergraterThanZero": "Please Enter Number greater Than Zero", "PleaseEntera1or2-digit": "Please insert one or two-digit number", "informationRequired": "Information Required", "PleaseEnteraPositivenumber": "This field should contain a whole number greater than or equal to 1", "PleaseEnteranIntegerNumber": "Please enter an integer number", "isIntegerAndhasOneOrTwoDigits": "This field should contain whole numbers only", "NumberofPoDSiblings": "Number of PoD Siblings must be less than or equal to Number of Siblings", "NumberofPoDChildren": "Number of PoD Children must be less than or equal to Number of Emirati children", "PleaseEnterNumberBiggerThanZero": "Please enter number bigger than zero", "isRequired": "{{fieldName}} is a required field.", "lessThanOrEqual4": "This number should be less than or equal 4", "numberOfPODSpouses": "Number Of POD Spouses", "numberOfChildren": "Number Of Children", "numberOfSpouses": "Number Of Spouses", "totalIncome": "Total Income", "jobTitle": "Job Title", "LastName": "Last Name", "lastName": "Last Name", "localNumberFormatSubtext": "UAE mobile number", "MaritalStatus": "Marital Status", "maxSize": "Max {{size}}", "memberName": "Member Name", "mocd": "Ministry of Community Empowerment", "mustBeNumber": "This field must be a number.", "mustBePositive": "This field must have a positive value.", "next": "Next", "noDocumentsUpload": "No documents needed, please proceed to the next step.", "noEWENoFarmerDesc": "This service is for ewe registered only", "noEWENoFarmerTitle": "You cant apply for this service", "noFamilyMembersData": "No Family Members data available, please proceed to the next step.", "noEducationMembersData": "This allowance is for children enrolled in higher education aged 16 and over. Currently, there are no children between the ages of 16 and 24. Please proceed to the next step.", "numberOfHousehold": "Number of Household", "Occupations": "Employment Status", "Verify Emirates ID": "Verify Emirates ID", "ownerEWEBill": "Do you own this account from EWE?", "passportCopy": "Passport Copy", "passportNo": "Passport No.", "PassportNumber": "Passport Number", "pension": "Pension", "pensionAmount": "Pension Amount Monthly (in AED)", "PensiontAuthority": "Pension Authority", "PensiontAuthorityText": "Pension Authority", "PensionType": "Pension Type", "PensionAuthority": "Pension Authority", "personalDocuments": "Personal Documents", "personalInformation": "Personal Information", "EditReason": "Edit Reason", "personalInformationSubtext": "Please enter all required information below. All fields marked with an asterisk (*) are mandatory. Before proceeding with your application, please make sure you have all the required documents, and their validity meets the requirements.", "phoneNumber": "Phone Number", "PreferredEmail": "Email Address", "PreferredPhoneNumber": "Mobile Number", "RelatedEmiratesID": "Emirates ID registered with EWE", "relationship": "Relationship", "RentalIncome": "Rental Income", "RentalIncomes": "Rental Incomes", "rentalSource": "Rental Source", "RentalSource": "Rental Source", "RentalSourceText": "Rental Source", "RentAmount": "Rent Amount Monthly (in AED)", "requestAddedBody1": "Thank you for submitting an application for the Social Assistance Service.", "requestAddedBody1Farmer": "Thank you for applying to the Farmer Welfare program", "requestAddedBody2": "Your application for the service has been submitted and is under review.The Ministry of Community Empowerment will contact you soon.", "requestAddedTitle": "Your application has been successfully submitted and is under review.", "requestEditedBody1": "Thank you for applying to the Social Welfare program.", "requestEditedBody1Farmer": "Thank you for applying to the Farmer Welfare program.", "requestEditedBody2": "Your application has been submitted successfully and will be processed for review by the relevant department. The ministry will contact you soon.", "requestEditedTitle": "Your application has been submitted successfully and will be processed for review by the relevant department.", "requestNumber": "Request Number", "requestSubmitted": "Case Submitted", "reviewDetails": "Review Details", "reviewDetailsSubtext": "Please review the information and proceed as required.", "selectFiles": "Select File", "socialAidInformation": "Social Aid Information", "socialAidInformationSubtext": "Please enter all required information below. All fields marked with an asterisk (*) are mandatory.", "socialServices-sharjah": "Social Services Department - Sharjah", "status": "Status", "StatusCode": "Status", "submitRequest": "Submit Case", "submittedOn": "Submitted On", "thankYouForFeedback": "Thank you for your feedback.", "thisField": "This field", "tradeLicense": "Trade License", "tradeLicenseAmount": "Trade License Amount Monthly (in AED)", "TradeSourceText": "Trade Source", "uaeMobileNumberError": "Please provide a UAE number with the following format 05XXXXXXXX", "uaeIDNumberError": "Please enter a valid Emirates ID", "uaeResidenceVisa": "UAE Residence Visa", "universityDegree": "Certified Degree From University", "uploadingFile": "Uploading File", "useLengthError": "Please enter a number of 12 digits", "validWorkContractFamily": "Valid Work Contract", "wrongEmailAddress": "Please enter a valid email address.", "EnterEmirateIdForEWE": "Please enter the Emirates ID number registered with the farm account number from the Union Water and Electricity Company", "EntityReceivedFrom": "From what side?", "HowToKnowEWE": "How to find out the account number of the Union Water and Electricity Company.", "PleaseEnterEWENumber": "Please enter EWE account number for the farm", "ReceiveInflationAllowance": "Do you receive an inflation bonus?", "ReceiveSocialAid": "Do you receive social assistance?", "RegisteredWithEWE": "Are you registered with EWE?", "attachedDocumentsFarmer": "Attached documents", "complaintSuccessBody": "Thank you for submitting the {{inquiryType}}. You can check the status of this {{inquiryType}} at any time through the Inquiries / Suggestions page. We will contact you later if we need any additional information.", "complaintSuccessBodyForAnonymous": "Thank you for submitting the {{inquiryType}}. We will contact you later if we need any information.", "complaintSuccessCaseNumberTitle": "{{inquiryType}} number", "complaintSuccessDateTitle": "Submitted", "complaintSuccessTitle": "The {{inquiryType}} has been submitted successfully and is under review", "complaintTitle": "{{inquiryType}} Title", "familyMembersInformationFarmer": "Family member information", "farmerAidInformation": "Information related to farm owners aid", "farmerAidRquestAddedBody2": "Your request is under review and we will be contacted shortly.", "farmerRequestAdded": "Your request has been submitted successfully", "incomeInformationFarmer": "income information", "reviewDetailsFarmer": "Review details", "socialAidInformationFarmer": "Social assistance information", "Area": "Area", "Category": "Reason", "SubCategory": "Details", "EmiratesResd": "Emirate Residency", "Center": "Center", "youHaveToReadTerms": "You have to read the terms", "IsActiveStudent": "Is active student ?", "MilitaryServiceStatus": "Military", "Terminated": "Terminated", "jobseekerErrorMsg": "This assistance is temporary for six months - and you can only apply for it twice in the next five years.", "notApplicableErrorMsg": "you can not select another value with not applicable", "feedbackRecived": "Your feedback has been submitted, thank you!", "HaveChildrenCustody": "Do you have custody of your children", "levelOfEducation": "Enrolled in (level of education) ?", "ReceivedLocalSupport": "Are you or your spouse receiving any other local social support ?", "guardianIncome": "Guardian income (AED)", "PursuingHigherEducation": "Have you been pursuing higher education since turning 21 years old?", "PursuingMilitaryService": "Have you been pursuing military service since turning 21 years old?", "GuardianEmiratesID": "Guardian Emirates ID", "PensiontAmount": "Monthly Pension Amount (AED)", "noDataFound": "No Data Found", "swfProgram": "Social Subsidy Program", "InflationProgram": "Inflation Program", "theService": "Service", "completeInfo": "Complete", "IsPursuingHigherEducation": "Is Pursuing Higher Education since turning 21 years old?", "IsDraftedinMilitaryService": "Is Drafted in Military Service since turning 21 years old?", "addMoreDocs": "Add additional documents", "selectDocType": "Select the type of documennt you want to add", "PreferredEmailUsed": "Email (the certificate will be sent to this email)", "aboutWebsite": "About Website", "siteMap": "Sitemap", "previous": "Previous", "allowanceCalculator": "Allowance Calculator", "pages": "Pages", "more": "More", "Beneficiaries": "Beneficiaries", "Topic": "Topic", "FamilyHousholdName": "Family Houshold Name :", "khulasitQaidNumber": "Family book number", "ChildEligibilityforWomeninDifficulty": "Do you have custody of at least one child that meets the following criteria ?", "select": "Please select if applicable", "isRequiredField": "this is a required field", "NumberOfChildren": "How many children meet the above criteria?", "NumberOfChildrenLessThan25": "How many children less than 25?", "complaintServices": "Service", "complaintSubServices": "Sub Services", "generatingFamilyBook": "Please wait... Family book is being generated", "thisFieldshouldbeLess": "must be greater than 0 and less than or equal to children meet the above criteria", "greaterThanZero": "must be greater than 0", "localSupText": "(From any of these entities: Abu Dhabi Social Support Authority, Community Development Authority, Sharjah Social Services Department)", "localSupText2": "(Sheikh <PERSON> Housing Program, Abu Dhabi Housing authority, Mohammed Bin Rashid Housing Establishment, Directorate of Housing of Sharjah)", "Verified": "Verified", "PendingVerification": "Pending Verification", "complaintHeader": "Complaint Title", "inquiryHeader": "Inquiry Title", "suggestionHeader": "Suggestion Title", "thankYouHeader": "Thank You Title", "thankYouTitle": "Thank You", "thankYouTitleHeader": "Thank You Title", "numberOfSiblingsError": "Please enter a 2-digit number for number Of Siblings", "ApplyHousingAllowance": "Would you like to apply for housing allowance?", "IsHouseholOwnerResidentialProperty": "Household members sole owner of a constructed residential property", "ReceivingFederalLocalhousingsupport": "Do you or any of your household members receive any Federal or Local housing support from any of the below programs?", "ReceivingHousingAllowanceFromEmployer": "Do you or any of your employed family members receive a housing allowance from the employer? (This includes free accommodation or rent paid by the employer.)", "IsUtilityBillIssuedForFullyOwnedProperty": "Is their a utility bill issued for the fully owned residential property?", "FullOwnershipResidentialProperty": "Do you or any of your household members have full or ownership of a residential property?", "LivingSituation": "What is your living situation?", "ApplyEducationAllowance": "Do you want to apply for educational excellence allowance for this child/sibling?", "childCompletedSemesterInUniversity": "Has this child completed more than 1 semester in a university accredited by Ministry of Higher Education and Scientific Research?", "highSchoolCurriculuim": "Please select high school curriculum", "enrolledEducationStream": "Which public education stream was the child/sibling enrolled in?", "EmSATorAdvancedPlacementScores": "Do you want to upload your EmSAT or Advanced Placement scores?", "Age": "Age", "ApplyEducation": "Applying for Education Excellence for Higher Education", "applyedtrue": "Yes", "applyedfalse": "No", "ReceivesHousingSupportFromHusband": "Do you receive housing support from your divorced husband as per court ruling?", "ApplyInflationAllowance": "Would you like to apply for inflation allowance?", "ApplyUtilityAllowance": "Would you like to apply for utility allowance as well?", "UtilityProvider": "Please select your utility provider", "UtilityAccountNumber": "Please insert the utility account number you wish to receive utility allowance for", "IsEnrolledInNationalService": "Is the child currently enrolled in national service?", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "inquiry": "Inquiry", "suggestion": "Suggestion", "informationForm": "Reason for Applying", "InflationCategory": "Reason for applying", "discliamer": "Child can apply for Education Excellence Allowance upon completing national service", "discliamer2": "Child can apply for Education Excellence Allowance after completing one semester in an accredited university", "disabledFieldMessage": "You may update the preferred phone or preferred email under My Profile", "womanOver45": "Please upload the EID and custody document if available", "spacesnotallowed": "This field is a required field.", "housingNotEligibleMessage": "Based on your selected category, you are not eligible for Housing Allowance.", "educationNotEligibleMessage": "Based on your selected category, you are not eligible for Education Excellence Allowance.", "housingNotEligibleMessageMaritalStatus": "Based on your marital status, you are not eligible for Housing Allowance.", "educationNotEligibleMessageMaritalStatus": "Based on your marital status, you are not eligible for Education Excellence Allowance.", "cgpa": "Cumulative GPA (CGPA)", "enterCGPA": "Enter CGPA (e.g., 3.50)", "cgpaHelperText": "Enter a value between 0.00 and 4.00", "creditHours": "Credit Hours", "enterCreditHours": "Enter Credit Hours (e.g., 12.0)", "creditHoursHelperText": "Enter a value between 0.0 and 21.0", "invalidCGPAFormat": "CGPA must be in format X.XX (e.g., 3.50)", "cgpaRangeError": "CGPA must be between 0.00 and 4.00", "invalidCreditHoursFormat": "Credit Hours must be in format XX.X (e.g., 12.0)", "creditHoursRangeError": "Credit Hours must be between 0.0 and 21.0", "universityName": "University Name", "unaccreditedUniversityName": "Unaccredited University Name", "enterUnaccreditedUniversityName": "Enter university name"}